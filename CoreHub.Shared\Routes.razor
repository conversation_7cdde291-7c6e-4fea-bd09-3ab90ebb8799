﻿<CascadingAuthenticationState>
    <Router AppAssembly="typeof(Layout.MainLayout).Assembly">
        <Found Context="routeData">
            <RouteView RouteData="routeData" DefaultLayout="typeof(Layout.MainLayout)" />
            <FocusOnNavigate RouteData="routeData" Selector="h1" />
        </Found>
        <NotFound>
            <PageTitle>页面未找到 - 企业管理系统</PageTitle>
            <LayoutView Layout="typeof(Layout.MainLayout)">
                <p role="alert">抱歉，没有找到该页面。</p>
            </LayoutView>
        </NotFound>
    </Router>
</CascadingAuthenticationState>
