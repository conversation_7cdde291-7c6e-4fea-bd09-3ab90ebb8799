@using MudBlazor

<CascadingAuthenticationState>
    <Router AppAssembly="typeof(Layout.MainLayout).Assembly">
        <Found Context="routeData">
            <RouteView RouteData="routeData" DefaultLayout="typeof(Layout.MainLayout)" />
            <FocusOnNavigate RouteData="routeData" Selector="h1" />
        </Found>
        <NotFound>
            <LayoutView Layout="typeof(Layout.MainLayout)">
                <PageTitle>页面未找到 - 企业管理系统</PageTitle>
                <MudContainer MaxWidth="MaxWidth.Medium" Class="mt-8">
                    <MudPaper Class="pa-8" Elevation="4">
                        <div style="text-align: center;">
                            <MudIcon Icon="@Icons.Material.Filled.ErrorOutline"
                                    Size="Size.Large"
                                    Color="Color.Warning"
                                    Style="font-size: 4rem; margin-bottom: 1rem;" />
                            <MudText Typo="Typo.h4" Class="mb-4">页面未找到</MudText>
                            <MudText Typo="Typo.body1" Class="mb-4">
                                抱歉，您访问的页面不存在或已被移除。
                            </MudText>
                            <MudButton Variant="Variant.Filled"
                                      Color="Color.Primary"
                                      Href="/"
                                      StartIcon="@Icons.Material.Filled.Home">
                                返回首页
                            </MudButton>
                        </div>
                    </MudPaper>
                </MudContainer>
            </LayoutView>
        </NotFound>
    </Router>
</CascadingAuthenticationState>
