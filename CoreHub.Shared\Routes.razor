﻿@using MudBlazor

<CascadingAuthenticationState>
    <Router AppAssembly="typeof(Layout.MainLayout).Assembly">
        <Found Context="routeData">
            <RouteView RouteData="routeData" DefaultLayout="typeof(Layout.MainLayout)" />
            <FocusOnNavigate RouteData="routeData" Selector="h1" />
        </Found>
        <NotFound>
            <LayoutView Layout="typeof(Layout.MainLayout)">
                <PageTitle>页面未找到 - 企业管理系统</PageTitle>
                <MudContainer MaxWidth="MaxWidth.Medium" Class="mt-8">
                    <MudPaper Class="pa-8" Elevation="4">
                        <div style="text-align: center;">
                            <MudIcon Icon="@Icons.Material.Filled.ErrorOutline"
                                    Size="Size.Large"
                                    Color="Color.Warning"
                                    Style="font-size: 4rem; margin-bottom: 1rem;" />
                            <MudText Typo="Typo.h4" Class="mb-4">页面未找到</MudText>
                            <MudText Typo="Typo.body1" Class="mb-4">
                                抱歉，您访问的页面不存在或已被移除。
                            </MudText>
                            <MudText Typo="Typo.body2" Class="mb-6" Color="Color.Secondary">
                                请检查URL是否正确，或者从下面的选项中选择：
                            </MudText>

                            <MudStack Row Justify="Justify.Center" Spacing="3" Class="mb-4">
                                <MudButton Variant="Variant.Filled"
                                          Color="Color.Primary"
                                          Href="/"
                                          StartIcon="@Icons.Material.Filled.Home">
                                    返回首页
                                </MudButton>
                                <MudButton Variant="Variant.Outlined"
                                          Color="Color.Primary"
                                          OnClick="GoBack"
                                          StartIcon="@Icons.Material.Filled.ArrowBack">
                                    返回上页
                                </MudButton>
                            </MudStack>

                            <MudDivider Class="my-4" />

                            <MudText Typo="Typo.body2" Color="Color.Secondary">
                                如果您认为这是一个错误，请联系系统管理员。
                            </MudText>
                        </div>
                    </MudPaper>
                </MudContainer>
            </LayoutView>
        </NotFound>
    </Router>
</CascadingAuthenticationState>

@code {
    [Inject] private NavigationManager Navigation { get; set; } = default!;
    [Inject] private IJSRuntime JSRuntime { get; set; } = default!;

    private async Task GoBack()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("history.back");
        }
        catch
        {
            // 如果JavaScript调用失败，回到首页
            Navigation.NavigateTo("/");
        }
    }
}
