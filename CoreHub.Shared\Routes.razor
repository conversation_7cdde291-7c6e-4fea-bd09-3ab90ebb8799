@using MudBlazor
@using CoreHub.Shared.Components

<CascadingAuthenticationState>
    <Router AppAssembly="typeof(Layout.MainLayout).Assembly">
        <Found Context="routeData">
            <RouteView RouteData="routeData" DefaultLayout="typeof(Layout.MainLayout)" />
            <FocusOnNavigate RouteData="routeData" Selector="h1" />
        </Found>
        <NotFound>
            <LayoutView Layout="typeof(Layout.MainLayout)">
                <NotFoundComponent />
            </LayoutView>
        </NotFound>
    </Router>
</CascadingAuthenticationState>