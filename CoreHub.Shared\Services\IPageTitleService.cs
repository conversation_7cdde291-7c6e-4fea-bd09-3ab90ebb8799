namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 页面标题服务接口
    /// </summary>
    public interface IPageTitleService
    {
        /// <summary>
        /// 设置页面标题
        /// </summary>
        /// <param name="title">页面标题</param>
        Task SetTitleAsync(string title);

        /// <summary>
        /// 获取当前页面标题
        /// </summary>
        /// <returns>当前页面标题</returns>
        Task<string> GetCurrentTitleAsync();
    }
}
