// 页面标题辅助工具
window.PageTitleHelper = {
    // 立即更新页面标题
    updateTitle: function (title) {
        if (title && typeof title === 'string') {
            document.title = title;
        }
    },

    // 监听路由变化并更新标题
    initializeTitleWatcher: function () {
        // 监听 Blazor 路由变化
        const observer = new MutationObserver(function (mutations) {
            mutations.forEach(function (mutation) {
                if (mutation.type === 'childList') {
                    // 查找新添加的 PageTitle 元素
                    const pageTitleElements = document.querySelectorAll('title');
                    if (pageTitleElements.length > 0) {
                        const latestTitle = pageTitleElements[pageTitleElements.length - 1];
                        if (latestTitle && latestTitle.textContent) {
                            document.title = latestTitle.textContent;
                        }
                    }
                }
            });
        });

        // 开始观察 head 元素的变化
        const headElement = document.head;
        if (headElement) {
            observer.observe(headElement, {
                childList: true,
                subtree: true
            });
        }

        // 监听 popstate 事件（浏览器前进后退）
        window.addEventListener('popstate', function () {
            setTimeout(function () {
                const titleElements = document.querySelectorAll('title');
                if (titleElements.length > 0) {
                    const currentTitle = titleElements[titleElements.length - 1];
                    if (currentTitle && currentTitle.textContent) {
                        document.title = currentTitle.textContent;
                    }
                }
            }, 100);
        });
    },

    // 获取当前页面标题
    getCurrentTitle: function () {
        return document.title;
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function () {
    window.PageTitleHelper.initializeTitleWatcher();
});

// Blazor 重新连接后重新初始化
window.addEventListener('blazor:reconnected', function () {
    window.PageTitleHelper.initializeTitleWatcher();
});
