﻿@page "/counter"
@inject INotificationService NotificationService
@inject NavigationManager Navigation
@using Microsoft.AspNetCore.Components.Authorization

<PageTitle>计数器 - 企业管理系统</PageTitle>

<MudContainer MaxWidth="MaxWidth.Medium">
    <MudText Typo="Typo.h3" Class="mb-4">计数器演示</MudText>
    <MudButton StartIcon="@Icons.Material.Filled.Notifications" >
        测试通知
    </MudButton>

    <MudPaper Class="pa-4 mb-4">
        <MudText Typo="Typo.h4" Class="mb-3">当前计数: @currentCount</MudText>
        
        <!-- 通知按钮 -->
        <div class="mb-4">
            <MudButton Variant="Variant.Filled" 
                      Color="Color.Primary" 
                      Size="Size.Large" 
                      StartIcon="@Icons.Material.Filled.Notifications" 
                      OnClick="SendNotification"
                      Class="mr-2">
                发送通知
            </MudButton>
            <MudButton Variant="Variant.Outlined" 
                      Size="Size.Large" 
                      StartIcon="@Icons.Material.Filled.NotificationImportant" 
                      OnClick="SendCounterNotification"
                      Class="mr-2">
                发送计数通知
            </MudButton>
            <MudButton Variant="Variant.Text" 
                      Size="Size.Large" 
                      StartIcon="@Icons.Material.Filled.Settings" 
                      OnClick="RequestPermission">
                请求权限
            </MudButton>
        </div>

        <!-- 状态显示 -->
        @if (showResult)
        {
            <MudAlert Severity="@(isSuccess ? Severity.Success : Severity.Error)" Class="mb-3">
                <div class="d-flex align-center">
                    <MudIcon Icon="@(isSuccess ? Icons.Material.Filled.CheckCircle : Icons.Material.Filled.Error)" Class="mr-2" />
                    <span>@resultMessage</span>
                </div>
            </MudAlert>
        }

        <!-- 计数按钮 -->
        <MudButton Variant="Variant.Filled" 
                  Color="Color.Primary" 
                  OnClick="IncrementCount"
                  StartIcon="@Icons.Material.Filled.Add">
            点击计数
        </MudButton>
    </MudPaper>

    <!-- 按钮样式演示 -->
    <MudPaper Class="pa-4 mb-4">
        <MudText Typo="Typo.h6" Class="mb-3">按钮样式演示</MudText>
        <div class="d-flex gap-2 mb-3">
            <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="IncrementCount">Primary</MudButton>
            <MudButton Variant="Variant.Outlined" OnClick="IncrementCount">Outlined</MudButton>
            <MudButton Variant="Variant.Text" OnClick="IncrementCount">Text</MudButton>
        </div>
        
        <div class="mb-3">
            <MudSwitch T="bool" @bind-Value="isChecked" Label="开关状态" Color="Color.Primary" />
        </div>
    </MudPaper>

    <!-- 认证检查 -->
    <AuthorizeView>
        <Authorized>
            <MudAlert Severity="Severity.Success">
                <div class="d-flex align-center">
                    <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Class="mr-2" />
                    <span>您已登录，可以使用所有功能</span>
                </div>
            </MudAlert>
        </Authorized>
        <NotAuthorized>
            <MudAlert Severity="Severity.Warning" Class="mb-3">
                <div class="d-flex align-center">
                    <MudIcon Icon="@Icons.Material.Filled.Warning" Class="mr-2" />
                    <span>您尚未登录，功能受限</span>
                </div>
            </MudAlert>
            <MudButton Variant="Variant.Filled" 
                      Color="Color.Primary" 
                      OnClick="GoToLogin"
                      StartIcon="@Icons.Material.Filled.Login">
                前往登录
            </MudButton>
        </NotAuthorized>
    </AuthorizeView>
</MudContainer>

@code {
    private int currentCount = 0;
    private bool isChecked = false;
    private bool showResult = false;
    private bool isSuccess = false;
    private string resultMessage = "";

    private void IncrementCount()
    {
        currentCount++;
    }

    private async Task SendNotification()
    {
        try
        {
            await NotificationService.SendNotificationAsync("计数器通知", "这是一个来自计数器页面的测试通知");
            ShowResult(true, "通知发送成功");
        }
        catch (Exception ex)
        {
            ShowResult(false, $"通知发送失败: {ex.Message}");
        }
    }

    private async Task SendCounterNotification()
    {
        try
        {
            await NotificationService.SendNotificationAsync("计数更新", $"当前计数: {currentCount}");
            ShowResult(true, "计数通知发送成功");
        }
        catch (Exception ex)
        {
            ShowResult(false, $"计数通知发送失败: {ex.Message}");
        }
    }

    private async Task RequestPermission()
    {
        try
        {
            var hasPermission = await NotificationService.RequestPermissionAsync();
            ShowResult(hasPermission, hasPermission ? "权限获取成功" : "权限获取失败");
        }
        catch (Exception ex)
        {
            ShowResult(false, $"权限请求失败: {ex.Message}");
        }
    }

    private void ShowResult(bool success, string message)
    {
        isSuccess = success;
        resultMessage = message;
        showResult = true;
        StateHasChanged();

        // 3秒后自动隐藏结果
        _ = Task.Delay(3000).ContinueWith(_ =>
        {
            showResult = false;
            InvokeAsync(StateHasChanged);
        });
    }

    private void GoToLogin()
    {
        Navigation.NavigateTo("/login");
    }
}
