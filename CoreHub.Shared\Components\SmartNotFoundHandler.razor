@namespace CoreHub.Shared.Components
@using Microsoft.AspNetCore.Components.Routing
@implements IDisposable
@inject NavigationManager Navigation

@if (ShowNotFound)
{
    <NotFoundComponent RequestedPath="@CurrentPath" />
}

@code {
    private bool ShowNotFound = false;
    private string CurrentPath = "";
    
    protected override void OnInitialized()
    {
        // 监听导航变化
        Navigation.LocationChanged += OnLocationChanged;
        CheckCurrentLocation();
    }
    
    private void OnLocationChanged(object? sender, LocationChangedEventArgs e)
    {
        CheckCurrentLocation();
        InvokeAsync(StateHasChanged);
    }
    
    private void CheckCurrentLocation()
    {
        var uri = new Uri(Navigation.Uri);
        CurrentPath = uri.AbsolutePath;
        
        // 检查当前路径是否应该显示404
        // 这里可以添加更复杂的逻辑来判断路径是否有效
        ShowNotFound = ShouldShowNotFound(CurrentPath);
    }
    
    private bool ShouldShowNotFound(string path)
    {
        // 定义已知的有效路径
        var validPaths = new[]
        {
            "/",
            "/weather",
            "/404",
            "/not-found",
            "/login",
            "/dashboard",
            "/equipment",
            "/repair-orders",
            "/maintenance",
            "/users",
            "/departments",
            "/permissions"
        };
        
        // 检查是否是有效路径或以有效路径开头
        return !validPaths.Any(validPath => 
            path.Equals(validPath, StringComparison.OrdinalIgnoreCase) ||
            (validPath != "/" && path.StartsWith(validPath + "/", StringComparison.OrdinalIgnoreCase)));
    }
    
    public void Dispose()
    {
        Navigation.LocationChanged -= OnLocationChanged;
    }
}
